import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { authTables } from "@convex-dev/auth/server";

const applicationTables = {
  users: defineTable({
    // Auth + directory
    email: v.optional(v.string()),
    name: v.optional(v.string()),
    // Changed to array to support multiple roles
    roles: v.optional(v.array(v.union(
      v.literal("partner"),
      v.literal("sales"),
      v.literal("ops"),
      v.literal("accounting"),
      v.literal("admin"),
      v.literal("superadmin"),
    ))),
    // Keep single role for backward compatibility
    role: v.optional(v.union(
      v.literal("partner"),
      v.literal("sales"),
      v.literal("ops"),
      v.literal("accounting"),
      v.literal("admin"),
      v.literal("superadmin"),
    )),
    tier: v.optional(v.union(v.literal("trusted"), v.literal("elite"), v.literal("diamond"))),
    // Enhanced Partner profile fields
    // Personal Info
    fullName: v.optional(v.string()),
    idDocStorageId: v.optional(v.id("_storage")),
    preferredCommunication: v.optional(v.array(v.union(
      v.literal("whatsapp"), 
      v.literal("email"), 
      v.literal("telegram")
    ))),
    telegram: v.optional(v.string()),
    whatsapp: v.optional(v.string()),
    // Company Info
    companyName: v.optional(v.string()),
    xProfile: v.optional(v.string()),
    companyType: v.optional(v.string()),
    companyTypeOther: v.optional(v.string()),
    roleTitle: v.optional(v.string()),
    roleTitleOther: v.optional(v.string()),
    
    // Internal assignment
    internalPocId: v.optional(v.id("users")),
    
    // Payment Details
    billingAddress: v.optional(v.string()),
    preferredPaymentMethod: v.optional(v.union(v.literal("bank"), v.literal("usdt"))),
    
    // Terms acceptance
    termsAcceptedVersion: v.optional(v.string()),
    termsAcceptedAt: v.optional(v.number()),
    
    // Auto-generated referral link
    defaultReferralCode: v.optional(v.string()),
    
    // Auth fields required by Convex Auth
    image: v.optional(v.string()),
    emailVerificationTime: v.optional(v.number()),
    phone: v.optional(v.string()),
    phoneVerificationTime: v.optional(v.number()),
    
    // State
    approved: v.optional(v.boolean()),
    profileCompleted: v.optional(v.boolean()),
    softDeletedAt: v.optional(v.number()),
  })
    .index("by_email", ["email"])
    .index("by_role", ["role"])
    .index("by_tier", ["tier"]),

  // Pre-approval list for signups
  whitelist: defineTable({
    email: v.string(),
    invitedBy: v.optional(v.id("users")),
  }).index("by_email", ["email"]),

  // Assignment (partner -> sales)
  assignments: defineTable({
    partnerId: v.id("users"),
    salesUserId: v.id("users"),
  })
    .index("by_partner", ["partnerId"])
    .index("by_sales", ["salesUserId"]),

  // Partner-owned referral short links
  referralLinks: defineTable({
    ownerId: v.id("users"),
    code: v.string(),
    destinationUrl: v.string(),
    title: v.optional(v.string()),
  })
    .index("by_code", ["code"])
    .index("by_owner", ["ownerId"]),

  // Click + form attribution
  referralClicks: defineTable({
    linkId: v.id("referralLinks"),
    ip: v.optional(v.string()),
    userAgent: v.optional(v.string()),
    utm: v.optional(v.object({ 
      source: v.optional(v.string()), 
      medium: v.optional(v.string()), 
      campaign: v.optional(v.string()) 
    })),
  }).index("by_link", ["linkId"]),

  // Lead submission ("Connect Us")
  leads: defineTable({
    partnerId: v.id("users"),
    salesUserId: v.optional(v.id("users")),
    referralLinkId: v.optional(v.id("referralLinks")),
    company: v.string(),
    website: v.optional(v.string()),
    twitter: v.optional(v.string()),
    pocName: v.optional(v.string()),
    pocRole: v.optional(v.string()),
    notes: v.optional(v.string()),
    status: v.union(v.literal("warm"), v.literal("cold"), v.literal("won"), v.literal("lost")),
    telegramGroupUrl: v.optional(v.string()),
    approved: v.optional(v.boolean()),
  })
    .index("by_partner", ["partnerId"])
    .index("by_status", ["status"])
    .index("by_sales", ["salesUserId"])
    .index("by_referral", ["referralLinkId"]),

  // Deals & financials
  deals: defineTable({
    leadId: v.optional(v.id("leads")),
    partnerId: v.id("users"),
    salesUserId: v.optional(v.id("users")),
    projectName: v.string(),
    dealType: v.string(),
    status: v.union(v.literal("in_progress"), v.literal("closed"), v.literal("lost"), v.literal("paid")),
    totalTokens: v.optional(v.number()),
    receivedTokens: v.optional(v.number()),
    liquidatedTokens: v.optional(v.number()),
    liquidationUsd: v.optional(v.number()),
    dealValueUsd: v.optional(v.number()),
    commissionPct: v.number(),
    commissionDueTokenUsd: v.optional(v.number()),
    commissionDueFiatUsd: v.optional(v.number()),
    commissionPendingUsd: v.optional(v.number()),
    lastUpdatedAt: v.number(),
  })
    .index("by_partner", ["partnerId"])
    .index("by_partner_status", ["partnerId", "status"])
    .index("by_sales", ["salesUserId"])
    .index("by_value", ["dealValueUsd"])
    .index("by_lead", ["leadId"]),

  // Earnings rollups & withdrawals
  withdrawals: defineTable({
    partnerId: v.id("users"),
    amountUsd: v.number(),
    method: v.union(v.literal("usdt"), v.literal("bank")),
    walletAddress: v.optional(v.string()),
    bankDetails: v.optional(v.string()),
    invoiceStorageId: v.optional(v.id("_storage")),
    status: v.union(v.literal("in_review"), v.literal("approved"), v.literal("paid"), v.literal("rejected")),
    txIdOrRef: v.optional(v.string()),
  })
    .index("by_partner", ["partnerId"])
    .index("by_status", ["status"]),

  // Versioned terms and acceptance
  termsVersions: defineTable({
    version: v.string(),
    title: v.string(),
    bodyStorageId: v.id("_storage"),
    publishedAt: v.number(),
  }).index("by_version", ["version"]),

  termsAcceptances: defineTable({
    userId: v.id("users"),
    version: v.string(),
    acceptedAt: v.number(),
  }).index("by_user", ["userId"]).index("by_version", ["version"]),

  // Resources (case studies, decks)
  resources: defineTable({
    title: v.string(),
    category: v.optional(v.string()),
    storageId: v.id("_storage"),
    audience: v.union(
      v.literal("all"),
      v.literal("trusted"),
      v.literal("elite"),
      v.literal("diamond"),
      v.literal("internal")
    ),
  }).index("by_audience", ["audience"]),

  // Messages (Contact Us, feedback)
  messages: defineTable({
    fromUserId: v.id("users"),
    toUserId: v.optional(v.id("users")),
    body: v.string(),
    kind: v.union(v.literal("feedback"), v.literal("support")),
    resolved: v.boolean(),
  }).index("by_to", ["toUserId"]).index("by_from", ["fromUserId"]),

  // Audit logs
  auditLogs: defineTable({
    actorUserId: v.id("users"),
    action: v.string(),
    entity: v.string(),
    meta: v.optional(v.any()),
    at: v.number(),
  }).index("by_time", ["at"]),
};

export default defineSchema({
  ...authTables,
  ...applicationTables,
});
