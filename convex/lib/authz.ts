import { getAuthUserId } from "@convex-dev/auth/server";
import { QueryCtx, MutationCtx } from "../_generated/server";

type Role = "partner" | "sales" | "ops" | "accounting" | "admin" | "superadmin";

// Helper function to check if user has any of the required roles
function hasRole(userRoles: Role[] | undefined, userRole: Role | undefined, requiredRoles: Role[] | Role | "any"): boolean {
  if (requiredRoles === "any") return true;
  
  const required = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
  
  // Check new roles array first
  if (userRoles && userRoles.length > 0) {
    return userRoles.some(role => required.includes(role));
  }
  
  // Fallback to single role for backward compatibility
  if (userRole) {
    return required.includes(userRole);
  }
  
  // Default to partner if no role is set
  return required.includes("partner");
}

export async function requireUser(
  ctx: QueryCtx | MutationCtx,
  requiredRoles: Role[] | Role | "any" = "any"
) {
  const userId = await getAuthUserId(ctx);
  if (!userId) {
    throw new Error("Authentication required");
  }

  const user = await ctx.db.get(userId);
  if (!user) {
    throw new Error("User not found");
  }

  if (!user.approved) {
    throw new Error("User not approved");
  }

  // Check role authorization
  if (!hasRole(user.roles, user.role, requiredRoles)) {
    const roleStr = Array.isArray(requiredRoles) ? requiredRoles.join(", ") : requiredRoles;
    throw new Error(`Access denied. Required role(s): ${roleStr}`);
  }

  return user;
}

export async function getCurrentUser(ctx: QueryCtx | MutationCtx) {
  const userId = await getAuthUserId(ctx);
  if (!userId) return null;
  
  return await ctx.db.get(userId);
}

export function getUserRoles(user: any): Role[] {
  // Return new roles array if it exists
  if (user.roles && user.roles.length > 0) {
    return user.roles;
  }
  
  // Fallback to single role
  if (user.role) {
    return [user.role];
  }
  
  // Default to partner
  return ["partner"];
}

export function userHasRole(user: any, role: Role): boolean {
  const userRoles = getUserRoles(user);
  return userRoles.includes(role);
}

export function userHasAnyRole(user: any, roles: Role[]): boolean {
  const userRoles = getUserRoles(user);
  return roles.some(role => userRoles.includes(role));
}
