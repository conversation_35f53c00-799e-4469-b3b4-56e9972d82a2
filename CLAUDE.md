# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🚀 Common Commands

```bash
npm run dev                  # Start both frontend (Vite) and backend (Convex) servers
npm run dev:frontend         # Start only frontend development server with Vite
npm run dev:backend          # Start only Convex backend development server
npm run build                # Build frontend with Vite
npm run lint                 # Run full lint check: TypeScript + Convex + Vite build
```

## 🏗️ Architecture Overview

This is a **Partner Referral Management System** built with:
- **Backend**: Convex (serverless backend with real-time data)
- **Frontend**: React + Vite + TypeScript
- **UI**: shadcn/ui with Tailwind CSS v4
- **Authentication**: Convex Auth with anonymous auth enabled
- **Database**: Convex (cloud-hosted)

### Project Structure
```
src/                 # Frontend React app
convex/              # Backend functions and schema
  _generated/        # Auto-generated Convex files (don't edit)
  schema.ts         # Database schema definition
  auth.ts           # Authentication configuration
  router.ts         # HTTP routes (separate from auth routes)
docs/                # Design rules and guidelines
```

### Core Architecture Patterns

**Backend (Convex)**:
- File-based routing: `convex/users.ts` → `api.users.functionName`
- Function types: `query` (read), `mutation` (write), `action` (external API calls)
- Internal functions: `internalQuery`, `internalMutation`, `internalAction`
- Always use new function syntax with `args` and `returns` validators
- HTTP endpoints defined in `convex/router.ts` (not `convex/http.ts` to protect auth)

**Frontend**:
- React 19 with functional components + hooks
- Vite for development and building
- Strict TypeScript configuration
- shadcn/ui components with "new-york" style
- ConvexAuthProvider wraps the entire app

## 📊 Database Schema (Key Tables)

**Core entities in `convex/schema.ts`:**
- `users`: Partners, sales staff, admins with roles and tier system
- `leads`: Lead submissions with status tracking (warm/cold/won/lost)
- `deals`: Financial transactions with commission tracking
- `referralLinks`: Partner-owned tracking links with codes
- `referralClicks`: Click attribution and analytics
- `withdrawals`: Partner payout requests and status
- `resources`: Tier-gated content (case studies, decks)

**Important patterns:**
- All tables have `_id` and `_creationTime` system fields
- Use `v.id("tableName")` validators for references
- Indexes follow naming: `by_field1_and_field2` format
- Role-based access with `partner`, `sales`, `ops`, `accounting`, `admin`, `superadmin`

## 🎨 Design System Rules

**Strict design constraints from `docs/DESIGN-RULES.md`:**

1. **Typography**: Only 4 font sizes, 2 weights (Semibold for headings, Regular for body)
2. **Spacing**: All values must be divisible by 8 or 4 (8pt grid system)
3. **Colors**: 60/30/10 rule (60% neutral, 30% complementary, 10% accent)
4. **Components**: Use shadcn/ui with "new-york" style, OKLCH colors, Tailwind v4

**Implementation details:**
- Use `@theme` directive instead of `@layer base`
- Components have `data-slot` attributes for styling
- Path alias: `@/` maps to `src/`

## 🔐 Authentication & Authorization

- **Convex Auth** with anonymous authentication enabled
- **Role-based permissions** throughout the system
- **Tier system**: trusted → elite → diamond for partners
- **Assignment system**: Partners assigned to sales team members
- Profile completion flow for partners

## 🛠️ Development Guidelines

**Convex Function Patterns:**
- Always include `args` and `returns` validators
- Use `ctx.runQuery/Mutation/Action` for internal calls
- Import from `./_generated/server` for function definitions
- File-based routing: function in `convex/users.ts` → `api.users.functionName`
- Internal functions use `internal.users.functionName`

**Frontend Patterns:**
- React Server Components not used (this is a Vite SPA)
- Use ConvexReactClient for real-time data binding
- TypeScript strict mode enabled
- Import shadcn/ui components as needed

**Key Files to Understand:**
- `convex/schema.ts`: Complete data model
- `convex/auth.config.ts`: Authentication setup
- `convex/router.ts`: HTTP API routes
- `src/App.tsx`: Main application component
- `docs/CONVEX-RULES.md`: Detailed Convex development guidelines

## 🚨 Important Notes

- **Don't edit** `convex/_generated/` files (auto-generated)
- **HTTP routes** go in `convex/router.ts`, not `convex/http.ts` (protects auth routes)
- **Follow design rules** strictly: 4 font sizes, 8pt grid, 60/30/10 colors
- **Use proper validators** for all Convex functions
- **Connected to deployment**: `greedy-okapi-741` on Convex dashboard

## 🧪 Testing & Quality

```bash
# Type checking
npx tsc -p convex -noEmit --pretty false
npx tsc -p . -noEmit --pretty false

# Development verification
convex dev --once    # Check backend
vite build          # Check frontend build
```

This codebase emphasizes real-time partner management with strict design consistency and comprehensive role-based access control.