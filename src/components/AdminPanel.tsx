import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";

import { SuperAdminPanel } from "./admin/panels/SuperAdminPanel";
import { AdminPanel as AdminPanelComponent } from "./admin/panels/AdminPanel";
import { OpsPanel } from "./admin/panels/OpsPanel";
import { AccountingPanel } from "./admin/panels/AccountingPanel";

export function AdminPanel() {
  const user = useQuery(api.users.myProfile);

  if (!user) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Helper function to get user roles
  const getUserRoles = (user: any) => {
    if (user.roles && user.roles.length > 0) {
      return user.roles;
    }
    if (user.role) {
      return [user.role];
    }
    return ["partner"];
  };

  const userRoles = getUserRoles(user);
  const hasRole = (role: string) => userRoles.includes(role);

  // Determine which panel to show based on highest priority role
  if (hasRole("superadmin")) {
    return <SuperAdminPanel />;
  } else if (hasRole("admin")) {
    return <AdminPanelComponent />;
  } else if (hasRole("ops")) {
    return <OpsPanel />;
  } else if (hasRole("accounting")) {
    return <AccountingPanel />;
  } else {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">Access Denied</h2>
          <p className="text-gray-600">You don't have permission to access admin features.</p>
          <p className="text-sm text-gray-500 mt-2">
            Available roles: superadmin, admin, ops, accounting
          </p>
        </div>
      </div>
    );
  }
}