import { useState, useEffect } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";

interface LeadSubmissionFormProps {
  referralCode?: string;
}

export function LeadSubmissionForm({ referralCode }: LeadSubmissionFormProps) {
  const referralLink = useQuery(
    api.referrals.getLinkByCode,
    referralCode ? { code: referralCode } : "skip"
  );
  const submitLead = useMutation(api.referrals.submitLeadFromReferral);
  const trackClick = useMutation(api.referrals.trackClick);
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    company: "",
    website: "",
    twitter: "",
    pocName: "",
    pocRole: "",
    notes: "",
  });

  // Track the referral click when component loads
  useEffect(() => {
    if (referralCode && referralLink) {
      trackClick({
        code: referralCode,
        ip: undefined, // Could be populated from server
        userAgent: navigator.userAgent,
      }).catch(console.error);
    }
  }, [referralCode, referralLink, trackClick]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!referralCode) {
      toast.error("Invalid referral link");
      return;
    }

    if (!formData.company || !formData.pocName || !formData.pocRole) {
      toast.error("Please fill in all required fields");
      return;
    }

    try {
      setIsSubmitting(true);
      
      await submitLead({
        referralCode,
        company: formData.company,
        website: formData.website || undefined,
        twitter: formData.twitter || undefined,
        pocName: formData.pocName,
        pocRole: formData.pocRole,
        notes: formData.notes || undefined,
      });
      
      toast.success("Lead submitted successfully! We'll be in touch soon.");
      
      // Reset form
      setFormData({
        company: "",
        website: "",
        twitter: "",
        pocName: "",
        pocRole: "",
        notes: "",
      });
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to submit lead");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (referralCode && referralLink === undefined) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (referralCode && !referralLink) {
    return (
      <div className="max-w-2xl mx-auto mt-20 p-8 bg-card rounded-lg shadow-sm border border-border">
        <div className="text-center">
          <h2 className="text-size-2 font-semibold text-foreground mb-4">Invalid Referral Link</h2>
          <p className="text-paragraph">
            The referral code you used is not valid or has expired.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background py-12">
      <div className="max-w-2xl mx-auto">
        <div className="bg-card rounded-lg shadow-sm border border-border p-8">
          <div className="text-center mb-8">
            <h1 className="text-size-1 font-semibold text-foreground mb-2">Connect Us</h1>
            <p className="text-paragraph">
              Submit your project details and we'll connect you with the right team
            </p>
            {referralLink && (
              <div className="mt-4 p-4 bg-accent/10 border border-accent/20 rounded-lg">
                <p className="text-size-4 text-stroke">
                  <strong>Referred by:</strong> {referralLink.ownerName}
                  {referralLink.ownerCompany && ` from ${referralLink.ownerCompany}`}
                </p>
              </div>
            )}
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-grid-6">
              <div>
                <label className="block text-size-4 font-semibold text-foreground mb-grid-2">
                  Company Name / Project Name *
                </label>
                <input
                  type="text"
                  value={formData.company}
                  onChange={(e) => setFormData({ ...formData, company: e.target.value })}
                  className="auth-input-field"
                  placeholder="Enter your company or project name"
                  required
                  maxLength={100}
                />
              </div>

              <div>
                <label className="block text-size-4 font-semibold text-foreground mb-grid-2">
                  Website
                </label>
                <input
                  type="url"
                  value={formData.website}
                  onChange={(e) => setFormData({ ...formData, website: e.target.value })}
                  className="auth-input-field"
                  placeholder="https://yourwebsite.com"
                />
              </div>

              <div>
                <label className="block text-size-4 font-semibold text-foreground mb-grid-2">
                  X / Twitter Link
                </label>
                <input
                  type="text"
                  value={formData.twitter}
                  onChange={(e) => setFormData({ ...formData, twitter: e.target.value })}
                  className="auth-input-field"
                  placeholder="@yourhandle or https://x.com/yourhandle"
                />
              </div>

              <div>
                <label className="block text-size-4 font-semibold text-foreground mb-grid-2">
                  Name of POC from the Project *
                </label>
                <input
                  type="text"
                  value={formData.pocName}
                  onChange={(e) => setFormData({ ...formData, pocName: e.target.value })}
                  className="auth-input-field"
                  placeholder="Point of contact name"
                  required
                  maxLength={100}
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-size-4 font-semibold text-foreground mb-grid-2">
                  Position of POC *
                </label>
                <input
                  type="text"
                  value={formData.pocRole}
                  onChange={(e) => setFormData({ ...formData, pocRole: e.target.value })}
                  className="auth-input-field"
                  placeholder="e.g., CEO, CTO, Founder, Business Development"
                  required
                  maxLength={100}
                />
              </div>
            </div>

            <div>
              <label className="block text-size-4 font-semibold text-foreground mb-grid-2">
                Notes / Comments (Optional)
              </label>
              <textarea
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                rows={4}
                className="auth-input-field"
                placeholder="Any additional information about your project or specific requirements..."
                maxLength={500}
              />
              <p className="text-size-4 text-paragraph mt-1">
                {formData.notes.length}/500 characters
              </p>
            </div>

            <div className="bg-card p-4 rounded-lg border border-border">
              <h3 className="text-size-3 font-semibold text-foreground mb-2">What happens next?</h3>
              <ul className="text-size-4 text-paragraph space-y-1">
                <li>• Your submission will be reviewed by our operations team</li>
                <li>• If approved, we'll set up a dedicated Telegram group</li>
                <li>• You'll be connected with the appropriate team members</li>
                <li>• We'll guide you through the next steps of the process</li>
              </ul>
            </div>

            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-primary text-primary-foreground px-6 py-4 rounded-md hover:bg-primary-hover transition-colors disabled:opacity-50 font-medium text-lg"
            >
              {isSubmitting ? "Submitting..." : "Submit Lead"}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}
