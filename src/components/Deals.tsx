import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { useState } from "react";
import { toast } from "sonner";

export function Deals() {
  const user = useQuery(api.users.myProfile);
  const deals = useQuery(api.deals.listForPartner, {});
  const createDeal = useMutation(api.deals.create);
  const updateDeal = useMutation(api.deals.update);
  const fixExistingDeals = useMutation(api.deals.fixExistingClosedDeals);
  
  const [isCreating, setIsCreating] = useState(false);
  const [formData, setFormData] = useState({
    partnerId: "",
    projectName: "",
    dealType: "",
    status: "in_progress" as "in_progress" | "closed" | "lost" | "paid",
    dealValueUsd: "",
    commissionPct: "",
    totalTokens: "",
    receivedTokens: "",
    liquidatedTokens: "",
    liquidationUsd: "",
    commissionDueTokenUsd: "",
    commissionDueFiatUsd: "",
    commissionPendingUsd: "",
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.partnerId || !formData.projectName || !formData.dealType) {
      toast.error("Partner, project name, and deal type are required");
      return;
    }

    try {
      await createDeal({
        partnerId: formData.partnerId as Id<"users">,
        projectName: formData.projectName,
        dealType: formData.dealType,
        status: formData.status,
        dealValueUsd: formData.dealValueUsd ? parseFloat(formData.dealValueUsd) : undefined,
        commissionPct: parseFloat(formData.commissionPct) || 5,
        totalTokens: formData.totalTokens ? parseFloat(formData.totalTokens) : undefined,
        receivedTokens: formData.receivedTokens ? parseFloat(formData.receivedTokens) : undefined,
        liquidatedTokens: formData.liquidatedTokens ? parseFloat(formData.liquidatedTokens) : undefined,
        liquidationUsd: formData.liquidationUsd ? parseFloat(formData.liquidationUsd) : undefined,
        commissionDueTokenUsd: formData.commissionDueTokenUsd ? parseFloat(formData.commissionDueTokenUsd) : undefined,
        commissionDueFiatUsd: formData.commissionDueFiatUsd ? parseFloat(formData.commissionDueFiatUsd) : undefined,
        commissionPendingUsd: formData.commissionPendingUsd ? parseFloat(formData.commissionPendingUsd) : undefined,
      });
      toast.success("Deal created successfully");
      setFormData({
        partnerId: "",
        projectName: "",
        dealType: "",
        status: "in_progress",
        dealValueUsd: "",
        commissionPct: "",
        totalTokens: "",
        receivedTokens: "",
        liquidatedTokens: "",
        liquidationUsd: "",
        commissionDueTokenUsd: "",
        commissionDueFiatUsd: "",
        commissionPendingUsd: "",
      });
      setIsCreating(false);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to create deal");
    }
  };

  const handleStatusUpdate = async (dealId: Id<"deals">, status: "in_progress" | "closed" | "lost" | "paid") => {
    try {
      await updateDeal({ dealId, status });
      toast.success("Deal status updated");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update deal");
    }
  };

  const handleFixCommissions = async () => {
    try {
      const result = await fixExistingDeals({});
      toast.success(`Fixed commissions for ${result.dealsFixed} deals`);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to fix commissions");
    }
  };

  if (!user || deals === undefined) {
    return (
      <div className="flex justify-center items-center py-grid-8">
        <div className="flex flex-col items-center gap-grid-4">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-accent border-t-transparent"></div>
          <p className="text-size-4" style={{ color: "var(--muted-foreground)" }}>Loading deals...</p>
        </div>
      </div>
    );
  }

  const canCreateDeals = user.role && ["sales", "ops", "accounting", "admin", "superadmin"].includes(user.role);
  const canUpdateDeals = user.role && ["sales", "ops", "accounting", "admin", "superadmin"].includes(user.role);
  const canFixCommissions = user.role && ["admin", "superadmin", "accounting"].includes(user.role);

  return (
    <div className="space-y-grid-8 animate-fade-in">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-grid-4">
        <div className="space-y-grid-2">
          <h1 className="text-size-1">Deals</h1>
          <p className="text-size-3" style={{ color: "var(--muted-foreground)" }}>Track and manage your deal pipeline and commissions</p>
        </div>
        <div className="flex gap-grid-3">
          {canFixCommissions && (
            <button
              onClick={handleFixCommissions}
              className="btn-secondary hover-lift text-orange-400 border-orange-400 hover:bg-orange-400 hover:text-card-foreground"
            >
              🔧 Fix Commissions
            </button>
          )}
          {canCreateDeals && (
            <button
              onClick={() => setIsCreating(!isCreating)}
              className={`btn-gradient hover-lift ${isCreating ? 'bg-destructive hover:bg-destructive' : ''}`}
            >
              {isCreating ? "✕ Cancel" : "💼 Create New Deal"}
            </button>
          )}
        </div>
      </div>

      {canCreateDeals && isCreating && (
        <div className="glass-card animate-scale-in">
          <div className="card-content">
            <div className="flex items-center gap-grid-3 mb-grid-6">
              <div className="w-10 h-10 bg-gradient-accent rounded-lg flex items-center justify-center">
                <span className="text-accent-foreground text-size-2">💼</span>
              </div>
              <h2 className="text-size-2">Create New Deal</h2>
            </div>
            <form onSubmit={handleSubmit} className="space-y-grid-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-grid-6">
              <div className="form-floating">
                <input
                  type="text"
                  value={formData.partnerId}
                  onChange={(e) => setFormData({ ...formData, partnerId: e.target.value })}
                  placeholder=" "
                  required
                  className="hover-glow"
                />
                <label>Partner ID *</label>
              </div>
              <div className="form-floating">
                <input
                  type="text"
                  value={formData.projectName}
                  onChange={(e) => setFormData({ ...formData, projectName: e.target.value })}
                  placeholder=" "
                  required
                  className="hover-glow"
                />
                <label>Project Name *</label>
              </div>
              <div className="form-floating">
                <input
                  type="text"
                  value={formData.dealType}
                  onChange={(e) => setFormData({ ...formData, dealType: e.target.value })}
                  placeholder=" "
                  required
                  className="hover-glow"
                />
                <label>Deal Type *</label>
              </div>
              <div>
                <select
                  value={formData.status}
                  onChange={(e) => setFormData({ ...formData, status: e.target.value as any })}
                  className="auth-input-field text-size-3 py-grid-3"
                >
                  <option value="in_progress">🔄 In Progress</option>
                  <option value="closed">✅ Closed</option>
                  <option value="lost">❌ Lost</option>
                  <option value="paid">💰 Paid</option>
                </select>
              </div>
              <div className="form-floating">
                <input
                  type="number"
                  step="0.01"
                  value={formData.dealValueUsd}
                  onChange={(e) => setFormData({ ...formData, dealValueUsd: e.target.value })}
                  placeholder=" "
                  className="hover-glow"
                />
                <label>Deal Value (USD)</label>
              </div>
              <div className="form-floating">
                <input
                  type="number"
                  step="0.1"
                  value={formData.commissionPct}
                  onChange={(e) => setFormData({ ...formData, commissionPct: e.target.value })}
                  placeholder=" "
                  className="hover-glow"
                />
                <label>Commission %</label>
              </div>
            </div>
            <div className="flex gap-grid-4 pt-grid-4">
              <button
                type="submit"
                className="btn-gradient hover-lift flex-1 sm:flex-none"
              >
                ✨ Create Deal
              </button>
              <button
                type="button"
                onClick={() => setIsCreating(false)}
                className="btn-secondary hover-lift"
              >
                Cancel
              </button>
            </div>
            </form>
          </div>
        </div>
      )}

      <div className="space-y-grid-6">
        {deals.length === 0 ? (
          <div className="glass-card">
            <div className="card-content text-center py-grid-8">
              <div className="w-16 h-16 bg-muted/20 rounded-full flex items-center justify-center mx-auto mb-grid-6">
                <span className="text-size-1">💼</span>
              </div>
              <h3 className="text-size-2 mb-grid-2">No deals found</h3>
              <p className="text-size-3 max-w-md mx-auto" style={{ color: "var(--muted-foreground)" }}>
                {canCreateDeals ? "Create your first deal to start tracking commissions and progress." : "Deals will appear here once they're created."}
              </p>
              {canCreateDeals && !isCreating && (
                <button
                  onClick={() => setIsCreating(true)}
                  className="btn-gradient hover-lift mt-grid-6"
                >
                  💼 Create Your First Deal
                </button>
              )}
            </div>
          </div>
        ) : (
          deals.map((deal, index) => (
            <div 
              key={deal._id} 
              className="glass-card hover-lift animate-slide-up"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="card-content">
                <div className="flex flex-col gap-grid-6">
                  {/* Header with project and status */}
                  <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-grid-4">
                    <div className="flex items-center gap-grid-3 flex-1 min-w-0">
                      <div className="w-10 h-10 bg-gradient-surface rounded-lg flex items-center justify-center flex-shrink-0">
                        <span className="text-foreground text-size-2">💼</span>
                      </div>
                      <div className="min-w-0 flex-1">
                        <h3 className="text-size-2 truncate">{deal.projectName}</h3>
                        <p className="text-size-4" style={{ color: "var(--muted-foreground)" }}>
                          {new Date(deal.lastUpdatedAt).toLocaleDateString()} • {deal.dealType}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex justify-start sm:justify-end">
                      <span className={`status-badge-compact ${
                        deal.status === "paid" ? "status-success" :
                        deal.status === "closed" ? "status-info" :
                        deal.status === "in_progress" ? "status-warning" :
                        "status-error"
                      }`}>
                        <span>
                          {deal.status === "paid" ? "💰" :
                           deal.status === "closed" ? "✅" :
                           deal.status === "in_progress" ? "🔄" :
                           "❌"}
                        </span>
                        <span>
                          {deal.status === "paid" ? "Paid" :
                           deal.status === "closed" ? "Closed" :
                           deal.status === "in_progress" ? "In Progress" :
                           "Lost"}
                        </span>
                      </span>
                    </div>
                  </div>

                  {/* Deal details */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-grid-4">
                    <div className="flex items-center gap-grid-2">
                      <span className="text-accent">📊</span>
                      <span className="text-size-4 text-foreground">
                        Commission: {deal.commissionPct}%
                      </span>
                    </div>
                    {deal.dealValueUsd && (
                      <div className="flex items-center gap-grid-2">
                        <span className="text-accent">💵</span>
                        <span className="text-size-4 text-foreground">
                          Value: ${deal.dealValueUsd.toLocaleString()}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Token details */}
                  {(deal.totalTokens || deal.receivedTokens || deal.liquidatedTokens) && (
                    <div className="p-grid-4 bg-surface-2 rounded-lg">
                      <h4 className="text-size-4 font-semibold mb-grid-3 flex items-center gap-grid-2">
                        <span className="text-accent">🪙</span>
                        Token Details
                      </h4>
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-grid-4 text-size-4">
                        {deal.totalTokens && (
                          <div>
                            <span style={{ color: "var(--muted-foreground)" }}>Total: </span>
                            <span className="font-semibold text-foreground">{deal.totalTokens.toLocaleString()}</span>
                          </div>
                        )}
                        {deal.receivedTokens && (
                          <div>
                            <span style={{ color: "var(--muted-foreground)" }}>Received: </span>
                            <span className="font-semibold text-foreground">{deal.receivedTokens.toLocaleString()}</span>
                          </div>
                        )}
                        {deal.liquidatedTokens && (
                          <div>
                            <span style={{ color: "var(--muted-foreground)" }}>Liquidated: </span>
                            <span className="font-semibold text-foreground">{deal.liquidatedTokens.toLocaleString()}</span>
                          </div>
                        )}
                      </div>
                      {deal.liquidationUsd && (
                        <p className="text-size-4 mt-grid-3">
                          <span style={{ color: "var(--muted-foreground)" }}>Liquidation Value: </span>
                          <span className="font-semibold text-foreground">${deal.liquidationUsd.toLocaleString()}</span>
                        </p>
                      )}
                    </div>
                  )}

                  {/* Commission details */}
                  {(deal.commissionDueTokenUsd || deal.commissionDueFiatUsd || deal.commissionPendingUsd) && (
                    <div className="p-grid-4 bg-accent/10 rounded-lg">
                      <h4 className="text-size-4 font-semibold mb-grid-3 flex items-center gap-grid-2">
                        <span className="text-accent">💰</span>
                        Commission Details
                      </h4>
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-grid-4 text-size-4">
                        {deal.commissionDueTokenUsd && (
                          <div>
                            <span style={{ color: "var(--muted-foreground)" }}>Token Commission: </span>
                            <span className="font-semibold text-foreground">${deal.commissionDueTokenUsd.toLocaleString()}</span>
                          </div>
                        )}
                        {deal.commissionDueFiatUsd && (
                          <div>
                            <span style={{ color: "var(--muted-foreground)" }}>Fiat Commission: </span>
                            <span className="font-semibold text-foreground">${deal.commissionDueFiatUsd.toLocaleString()}</span>
                          </div>
                        )}
                        {deal.commissionPendingUsd && (
                          <div>
                            <span style={{ color: "var(--muted-foreground)" }}>Pending: </span>
                            <span className="font-semibold text-foreground">${deal.commissionPendingUsd.toLocaleString()}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Commission validation warning */}
                  {(deal.status === "closed" || deal.status === "paid") && 
                   deal.dealValueUsd && deal.dealValueUsd > 0 &&
                   !deal.commissionDueTokenUsd && !deal.commissionDueFiatUsd && !deal.commissionPendingUsd && (
                    <div className="p-grid-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                      <div className="flex items-start gap-grid-3">
                        <span className="text-red-500 text-size-2 flex-shrink-0">⚠️</span>
                        <div>
                          <h4 className="text-size-4 font-semibold text-red-400 mb-grid-1">
                            Missing Commission Values
                          </h4>
                          <p className="text-size-4 text-red-300">
                            This {deal.status} deal worth ${deal.dealValueUsd.toLocaleString()} has no commission values set. 
                            Please run the commission fix or update the deal manually.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Actions */}
                  {canUpdateDeals && (
                    <div className="pt-grid-4 border-t border-border-subtle">
                      <select
                        value={deal.status}
                        onChange={(e) => handleStatusUpdate(deal._id, e.target.value as any)}
                        className="auth-input-field text-size-4 py-grid-2 w-full sm:w-auto"
                      >
                        <option value="in_progress">🔄 In Progress</option>
                        <option value="closed">✅ Closed</option>
                        <option value="lost">❌ Lost</option>
                        <option value="paid">💰 Paid</option>
                      </select>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
