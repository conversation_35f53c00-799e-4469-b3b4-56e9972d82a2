import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { toast } from "sonner";
import { Id } from "../../../../convex/_generated/dataModel";

import { AdminLayout } from "../shared/AdminLayout";
import { NavigationTabs } from "../shared/NavigationTabs";
import { StatsCard } from "../shared/StatsCard";
import { UserTable } from "../shared/UserTable";
import { LeadTable } from "../shared/LeadTable";
import { DealTable } from "../shared/DealTable";
import { WithdrawalTable } from "../shared/WithdrawalTable";
import { AuditLogViewer } from "../shared/AuditLogViewer";

export function SuperAdminPanel() {
  const [activeSection, setActiveSection] = useState("system");
  
  // Data queries
  const users = useQuery(api.admin.listUsers);
  const allLeads = useQuery(api.leads.listMine, {});
  const allDeals = useQuery(api.deals.listForPartner, {});
  const allWithdrawals = useQuery(api.earnings.listWithdrawals, {});
  const auditLogs = useQuery(api.admin.getAuditLogs, { limit: 50 });
  const pendingApprovals = useQuery(api.admin.listPendingApprovals);
  
  // Mutations
  const makeAdmin = useMutation(api.admin.makeAdmin);
  const approveUser = useMutation(api.admin.approveUser);
  const updateUserTier = useMutation(api.admin.updateUserTier);
  const approveLead = useMutation(api.leads.approve);
  const updateDeal = useMutation(api.deals.update);
  const updateWithdrawalStatus = useMutation(api.earnings.updateWithdrawalStatus);

  // Form state
  const [newAdminForm, setNewAdminForm] = useState({
    email: "",
    role: "admin" as const,
  });

  // Handlers
  const handleMakeAdmin = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const targetUser = users?.find(u => u.email === newAdminForm.email);
      if (!targetUser) {
        toast.error("User not found");
        return;
      }
      await makeAdmin({ userId: targetUser._id, role: newAdminForm.role });
      toast.success(`User promoted to ${newAdminForm.role}`);
      setNewAdminForm({ email: "", role: "admin" });
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to promote user");
    }
  };

  const handleApproveUser = async (userId: Id<"users">, approved: boolean) => {
    try {
      await approveUser({ userId, approved });
      toast.success(`User ${approved ? "approved" : "rejected"}`);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update user");
    }
  };

  const handleUpdateTier = async (userId: Id<"users">, tier: "trusted" | "elite" | "diamond") => {
    try {
      await updateUserTier({ userId, tier });
      toast.success(`User tier updated to ${tier}`);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update tier");
    }
  };

  const handleApproveLead = async (leadId: Id<"leads">, approved: boolean, details?: any) => {
    try {
      await approveLead({ 
        leadId, 
        approved, 
        telegramGroupUrl: details?.telegramGroupUrl,
        dealType: details?.dealType,
        commissionPct: details?.commissionPct,
      });
      toast.success(`Lead ${approved ? "approved and deal created" : "rejected"}`);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update lead");
    }
  };

  const handleUpdateDeal = async (dealId: Id<"deals">, updates: any) => {
    try {
      await updateDeal({ dealId, ...updates });
      toast.success("Deal updated successfully");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update deal");
    }
  };

  const handleUpdateWithdrawal = async (withdrawalId: Id<"withdrawals">, status: "approved" | "paid" | "rejected", txRef?: string) => {
    try {
      await updateWithdrawalStatus({ withdrawalId, status, txIdOrRef: txRef });
      toast.success(`Withdrawal ${status}`);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update withdrawal");
    }
  };

  // Calculate stats
  const totalPendingWithdrawals = allWithdrawals?.filter(w => w.status === "in_review")
    .reduce((sum, w) => sum + w.amountUsd, 0) || 0;
  
  const totalDealValue = allDeals?.reduce((sum, d) => sum + (d.dealValueUsd || 0), 0) || 0;

  const tabs = [
    { id: "system", label: "System Overview" },
    { id: "users", label: "User Management", count: pendingApprovals?.users.length || 0 },
    { id: "operations", label: "Operations", count: pendingApprovals?.leads.length || 0 },
    { id: "financial", label: "Financial", count: pendingApprovals?.withdrawals.length || 0 },
    { id: "roles", label: "Role Management" },
    { id: "audit", label: "System Audit" },
  ];

  return (
    <AdminLayout 
      title="Super Admin Panel" 
      description="Complete system administration and oversight"
    >
      <NavigationTabs 
        tabs={tabs}
        activeTab={activeSection}
        onTabChange={setActiveSection}
      />

      {activeSection === "system" && (
        <div className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatsCard
              title="System Health"
              value="Operational"
              description="All systems running normally"
              color="blue"
            />
            <StatsCard
              title="Total Users"
              value={users?.length || 0}
              description="Registered partners"
              color="green"
            />
            <StatsCard
              title="Total Deal Value"
              value={`$${totalDealValue.toLocaleString()}`}
              description="All-time revenue"
              color="purple"
            />
            <StatsCard
              title="Pending Actions"
              value={(pendingApprovals?.users.length || 0) + (pendingApprovals?.leads.length || 0) + (pendingApprovals?.withdrawals.length || 0)}
              description="Requiring attention"
              color="yellow"
            />
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <h3 className="text-lg font-semibold mb-4">Recent Activity</h3>
              <AuditLogViewer 
                auditLogs={auditLogs || []} 
                loading={!auditLogs}
                limit={5}
              />
            </div>

            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <h3 className="text-lg font-semibold mb-4">Quick Stats</h3>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span>Active Deals:</span>
                  <span className="font-medium">
                    {allDeals?.filter(d => d.status === "in_progress").length || 0}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Pending Withdrawals:</span>
                  <span className="font-medium">${totalPendingWithdrawals.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Total Leads:</span>
                  <span className="font-medium">{allLeads?.length || 0}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeSection === "users" && (
        <UserTable
          users={users || []}
          loading={!users}
          onApproveUser={handleApproveUser}
          onUpdateTier={handleUpdateTier}
        />
      )}

      {activeSection === "operations" && (
        <div className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <StatsCard
              title="Total Leads"
              value={allLeads?.length || 0}
              color="blue"
            />
            <StatsCard
              title="Approved Leads"
              value={allLeads?.filter(l => l.approved === true).length || 0}
              color="green"
            />
            <StatsCard
              title="Pending Leads"
              value={allLeads?.filter(l => l.approved === undefined).length || 0}
              color="yellow"
            />
            <StatsCard
              title="Active Deals"
              value={allDeals?.filter(d => d.status === "in_progress").length || 0}
              color="purple"
            />
          </div>

          <LeadTable
            leads={allLeads || []}
            loading={!allLeads}
            onApproveLead={handleApproveLead}
          />

          <div className="mt-8">
            <h3 className="text-lg font-semibold mb-4">All Deals</h3>
            <DealTable
              deals={allDeals || []}
              loading={!allDeals}
              onUpdateDeal={handleUpdateDeal}
              showFinancials={true}
            />
          </div>
        </div>
      )}

      {activeSection === "financial" && (
        <div className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <StatsCard
              title="Pending Withdrawals"
              value={`$${totalPendingWithdrawals.toLocaleString()}`}
              color="yellow"
            />
            <StatsCard
              title="Total Deal Value"
              value={`$${totalDealValue.toLocaleString()}`}
              color="green"
            />
            <StatsCard
              title="Total Requests"
              value={allWithdrawals?.length || 0}
              color="blue"
            />
          </div>

          <WithdrawalTable
            withdrawals={allWithdrawals || []}
            loading={!allWithdrawals}
            onUpdateWithdrawal={handleUpdateWithdrawal}
          />

          <div className="mt-8">
            <h3 className="text-lg font-semibold mb-4">Financial Deal Management</h3>
            <DealTable
              deals={allDeals || []}
              loading={!allDeals}
              onUpdateDeal={handleUpdateDeal}
              showFinancials={true}
            />
          </div>
        </div>
      )}

      {activeSection === "roles" && (
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold mb-4">Promote User to Admin Role</h3>
          <form onSubmit={handleMakeAdmin} className="space-y-4 max-w-md">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                User Email
              </label>
              <input
                type="email"
                value={newAdminForm.email}
                onChange={(e) => setNewAdminForm({ ...newAdminForm, email: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="<EMAIL>"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Role
              </label>
              <select
                value={newAdminForm.role}
                onChange={(e) => setNewAdminForm({ ...newAdminForm, role: e.target.value as any })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="admin">Admin</option>
                <option value="ops">Operations</option>
                <option value="accounting">Accounting</option>
                <option value="sales">Sales</option>
              </select>
            </div>
            <button
              type="submit"
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
            >
              Promote User
            </button>
          </form>
        </div>
      )}

      {activeSection === "audit" && (
        <AuditLogViewer 
          auditLogs={auditLogs || []} 
          loading={!auditLogs}
        />
      )}
    </AdminLayout>
  );
}