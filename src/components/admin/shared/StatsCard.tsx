interface StatsCardProps {
  title: string;
  value: string | number;
  description?: string;
  color?: "blue" | "green" | "yellow" | "purple" | "red";
  loading?: boolean;
}

const colorClasses = {
  blue: "text-blue-400",
  green: "text-green-400", 
  yellow: "text-yellow-400",
  purple: "text-purple-400",
  red: "text-red-400",
};

const valueColorClasses = {
  blue: "text-blue-300",
  green: "text-green-300",
  yellow: "text-yellow-300", 
  purple: "text-purple-300",
  red: "text-red-300",
};

const descriptionColorClasses = {
  blue: "text-blue-500",
  green: "text-green-500",
  yellow: "text-yellow-500",
  purple: "text-purple-500", 
  red: "text-red-500",
};

export function StatsCard({ 
  title, 
  value, 
  description, 
  color = "blue", 
  loading = false 
}: StatsCardProps) {
  if (loading) {
    return (
      <div className="rounded-lg p-6 animate-pulse" style={{ backgroundColor: 'var(--surface-1)', border: '1px solid var(--border-default)' }}>
        <div className="h-4 rounded w-3/4 mb-2" style={{ backgroundColor: 'var(--surface-2)' }}></div>
        <div className="h-8 rounded w-1/2 mb-2" style={{ backgroundColor: 'var(--surface-2)' }}></div>
        <div className="h-3 rounded w-full" style={{ backgroundColor: 'var(--surface-2)' }}></div>
      </div>
    );
  }

  const formattedValue = typeof value === 'number' ? value.toLocaleString() : value;

  return (
    <div className="rounded-lg p-6" style={{ backgroundColor: 'var(--surface-1)', border: '1px solid var(--border-default)' }}>
      <h3 className="text-size-3 font-semibold mb-2" style={{ color: 'var(--foreground)' }}>{title}</h3>
      <p className={`text-size-1 font-semibold ${valueColorClasses[color]} mb-1`}>
        {formattedValue}
      </p>
      {description && (
        <p className={`text-size-4 ${descriptionColorClasses[color]}`}>
          {description}
        </p>
      )}
    </div>
  );
}