import { useEffect, useState } from "react";
import { LeadSubmissionForm } from "./LeadSubmissionForm";

export function PublicLeadForm() {
  const [referralCode, setReferralCode] = useState<string | undefined>();

  useEffect(() => {
    // Get referral code from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const ref = urlParams.get('ref');
    if (ref) {
      setReferralCode(ref);
    }
  }, []);

  return <LeadSubmissionForm referralCode={referralCode} />;
}
