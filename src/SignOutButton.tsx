"use client";
import { useAuthActions } from "@convex-dev/auth/react";
import { useConvexAuth } from "convex/react";
import { useState } from "react";

export function SignOutButton() {
  const { isAuthenticated } = useConvexAuth();
  const { signOut } = useAuthActions();
  const [isSigningOut, setIsSigningOut] = useState(false);

  if (!isAuthenticated) {
    return null;
  }

  const handleSignOut = async () => {
    console.log("User signing out");
    setIsSigningOut(true);
    try {
      await signOut();
    } catch (error) {
      console.error("Sign out error:", error);
      setIsSigningOut(false);
    }
  };

  return (
    <button
      className="relative flex items-center gap-grid-2 px-grid-4 py-grid-2 rounded-lg font-semibold text-size-3 min-w-[100px] h-10 border-2 border-primary text-primary bg-transparent overflow-hidden group transition-colors duration-300 disabled:opacity-50"
      onClick={handleSignOut}
      disabled={isSigningOut}
    >
      {/* Fill animation background */}
      <div className="absolute inset-0 bg-primary transform -translate-x-full group-hover:translate-x-0 transition-transform duration-300 ease-out"></div>

      {/* Content */}
      <div className="relative z-10 flex items-center gap-grid-2 group-hover:text-primary-foreground transition-colors duration-300">
        {isSigningOut ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent"></div>
            <span className="hidden sm:inline">Signing out...</span>
          </>
        ) : (
          <>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
            <span className="hidden sm:inline">Sign out</span>
            <span className="sm:hidden">Exit</span>
          </>
        )}
      </div>
    </button>
  );
}
